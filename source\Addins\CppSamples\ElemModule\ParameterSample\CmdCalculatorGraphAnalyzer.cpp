#include "CmdCalculatorGraphAnalyzer.h"
#include "UTF8FileHelper.h"
#include "ElemModule.h"
#include "ElemModuleCommandIds.h"
#include "CommandRegister.h"
#include "IDocument.h"
#include "IModelView.h"
#include "UiDocumentViewUtils.h"
#include "IUserTransaction.h"
#include "IApplicationWindow.h"
#include "IApplication.h"
#include "ICommandManager.h"
#include "IElement.h"
#include "IElementRegenerationComponent.h"
#include "ICalculatorCollection.h"
#include "ICalculator.h"
#include "RegenDataId.h"
#include "ElementId.h"
#include "UiCommonDialog.h"
#include <sstream>
#include <fstream>
#include <set>
#include <map>
#include "EnableCompileWarning_The_LAST_IncludeInCpp.h"

using namespace gcmp;
using namespace Sample;

#define ID_CMD_CALCULATOR_GRAPH_ANALYZER L"CmdCalculatorGraphAnalyzer"

CmdCalculatorGraphAnalyzer::CmdCalculatorGraphAnalyzer()
    :CommandBase(ID_CMD_CALCULATOR_GRAPH_ANALYZER, true)
{
}

gcmp::OwnerPtr<gcmp::IAction> CmdCalculatorGraphAnalyzer::ExecuteCommand(const gcmp::CommandParameters& cmdParams)
{
    IDocument* pDoc = UiDocumentViewUtils::GetCurrentDocument();
    if (!pDoc)
    {
        // 可以添加用户提示：没有打开的文档
        return nullptr;
    }

    OwnerPtr<IUserTransaction> opUserTrans = IUserTransaction::Create(pDoc, L"分析计算器关联图");

    std::vector<CalculatorInfo> calculatorInfos;
    AnalyzeElementCalculators(pDoc, calculatorInfos);

    if (calculatorInfos.empty())
    {
        // 可以添加用户提示：没有找到计算器
        opUserTrans->Commit();
        return nullptr;
    }

    std::wstring mermaidGraph = GenerateMermaidGraph(calculatorInfos);

    // 将Mermaid图保存到文件
    std::wstring filePathName = gcmp::UiCommonDialog::ShowSaveFileDialog(GBMP_TR(L"请选择导出mermaid文件"),
        L"", GBMP_TR(L"markdown文件(*.md)"));

    // 使用UTF-8转换方式保存文件
    if (!filePathName.empty())
    {
        bool success = UTF8FileHelper::SaveMermaidGraphFile(filePathName, mermaidGraph, calculatorInfos.size());
        if (success)
        {
            // 可以添加用户提示：文件保存成功
        }
        else
        {
            // 可以添加用户提示：文件保存失败
        }
    }
    else
    {
        // 可以添加用户提示：用户取消了文件保存
    }

    opUserTrans->Commit();
    return nullptr;
}

void CmdCalculatorGraphAnalyzer::AnalyzeElementCalculators(IDocument* pDoc, std::vector<CalculatorInfo>& calculatorInfos)
{
    // 获取文档中的所有图元
    std::vector<IElement*> allElements = pDoc->GetAllElements();
    
    for (IElement* pElement : allElements)
    {
        if (!pElement)
            continue;

        // 获取图元的关联更新组件
        IElementRegenerationComponent* pRegenComponent = pElement->GetElementRegenerationComponent();
        if (!pRegenComponent)
            continue;

        // 创建计算器收集器
        OwnerPtr<ICalculatorCollection> opCalculators = ICalculatorCollection::Create(pDoc);
        if (!opCalculators)
            continue;

        // 获取图元的所有计算器
        pRegenComponent->GetCalculators(opCalculators.get());

        // 遍历计算器
        int calculatorCount = opCalculators->GetCalculatorCount();
        for (int i = 0; i < calculatorCount; i++)
        {
            const ICalculator* pCalculator = opCalculators->GetCalcualtorByIndex(i);
            if (!pCalculator)
                continue;

            CalculatorInfo info;
            info.calculatorName = pCalculator->GetCalculatorName();
            info.elementId = std::to_wstring(pElement->GetElementId().AsInt64());
            info.elementType = GetElementTypeName(pElement);
            info.outputDataId = pCalculator->GetRegenDataId();

            // 获取输入数据ID
            pCalculator->ReportInputDataIds(info.inputDataIds);

            calculatorInfos.push_back(info);
        }
    }
}

std::wstring CmdCalculatorGraphAnalyzer::GenerateMermaidGraph(const std::vector<CalculatorInfo>& calculatorInfos)
{
    std::wstringstream ss;
    ss << L"graph TD\n";

    // 创建节点映射，避免重复
    std::map<std::wstring, std::wstring> nodeMap;
    std::set<std::wstring> connections;

    // 为每个计算器创建节点
    for (size_t i = 0; i < calculatorInfos.size(); i++)
    {
        const CalculatorInfo& info = calculatorInfos[i];
        std::wstring nodeId = L"calc" + std::to_wstring(i);
        std::wstring nodeLabel = info.calculatorName + L"<br/>(" + info.elementType + L":" + info.elementId + L")";

        ss << L"    " << nodeId << L"[\"" << nodeLabel << L"\"]\n";

        // 记录输出数据ID到节点的映射
        std::wstring outputKey = RegenDataIdToString(info.outputDataId);
        nodeMap[outputKey] = nodeId;
    }

    // 创建连接
    for (size_t i = 0; i < calculatorInfos.size(); i++)
    {
        const CalculatorInfo& info = calculatorInfos[i];
        std::wstring currentNodeId = L"calc" + std::to_wstring(i);

        // 为每个输入数据ID查找对应的输出节点
        for (const RegenDataId& inputDataId : info.inputDataIds)
        {
            std::wstring inputKey = RegenDataIdToString(inputDataId);
            auto it = nodeMap.find(inputKey);
            if (it != nodeMap.end())
            {
                std::wstring connection = it->second + L" --> " + currentNodeId;
                if (connections.find(connection) == connections.end())
                {
                    ss << L"    " << connection << L"\n";
                    connections.insert(connection);
                }
            }
        }
    }

    return ss.str();
}

std::wstring CmdCalculatorGraphAnalyzer::RegenDataIdToString(const RegenDataId& dataId)
{
    std::wstringstream ss;
    ss << dataId.ObjectId << L"_" << dataId.DataId << L"_" << dataId.ExtendedId;
    return ss.str();
}

std::wstring CmdCalculatorGraphAnalyzer::GetElementTypeName(IElement* pElement)
{
    if (!pElement)
        return L"Unknown";

    // 尝试获取元素的类型名称
    // 可以根据具体的GDMP接口来获取更详细的类型信息
    // 这里提供一个基础实现，可以根据实际需要扩展

    // 获取元素的类名或类型信息
    // 注意：这里需要根据实际的GDMP接口来实现
    // 例如可能通过GetClassUid()或其他方法来获取类型信息

    return L"Element";
}

REGISTER_COMMAND(CmdCalculatorGraphAnalyzer);
