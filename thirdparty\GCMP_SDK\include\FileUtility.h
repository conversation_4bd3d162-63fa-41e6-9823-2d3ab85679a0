﻿// Owner: chenpy-a
// Co-Owner: liuc-h

#pragma once

#include "GcmpDevService.h"
#include <string>
#include <vector>

namespace gcmp
{
    class DirectoryEntry;
    class GCMP_DEV_SERVICE_EXPORT FileUtility
    {
    private:
        FileUtility(void);

    public:
        static bool CopyFileW(const std::wstring& srcFile,const std::wstring& destFile, bool bFailIfExists = false);

        static std::wstring GetAvailableFilePath(const std::wstring& fileFolder, const std::wstring& prefix, const std::wstring& ext);

        static std::vector<char> Load(const std::wstring &filePath);

        static std::vector<char> LoadInBinaryMode(const std::wstring &filePath);

        static std::string LoadINIFile(const std::wstring &filePath);

        static bool Save(const std::wstring &filePath, const char *data, int dataSize);

        static bool SaveInBinaryMode(const std::wstring &filePath, const char *data, int dataSize,bool isAppend = false);

        // 创建目录，可以一次递归创建多级。
        // 存在或者创建成功，返回true；否则返回false
        // param showDebugWarning，出错时是否弹出调试警告，默认弹出；调用者可自行控制
        static bool CreateDirectories(const std::wstring &path, bool showDebugWarning = true);

        // 删除文件或者目录，如果是目录则递归删除，返回删除文件和目录的数量
        // path：要删除的文件或者文件夹路径   asynchronous_cmd：是否异步起cmd进程删除(默认是同步删除文件夹，如果异步进程删除，需要保证文件后续的同步性)
        // 同步删除返回删除文件个数，异步删除返回>0为成功
        static int RemoveAll(const std::wstring &path, bool asynchronous_cmd = false);

        // 删除文件或者目录，如果是目录则递归删除，返回删除文件和目录的数量,function是过滤哪些文件不删除
        static int RemoveAllWithFilter(const std::wstring &path, const std::function<bool(const DirectoryEntry &entry)> &filter);
        // 复制文件或者目录
        static void Copy(const std::wstring& srcPath,const std::wstring& destPath);
        static void Copy(const std::wstring& srcPath,const std::wstring& destPath, const std::function<bool(const DirectoryEntry &entry)> &filter);
        //获取文件夹下所有指定类型的的文件(非递归)
        static std::vector<std::wstring> GetFiles(const std::wstring &path,const std::wstring &ext);
        //按过滤器获取文件夹下的的文件全路径(默认非递归)
        static std::vector<std::wstring> GetFilePathsByFilter(const std::wstring &path, std::function<bool(const DirectoryEntry &entry)> filter, bool recursively = false);

        static bool AppendHideAttribute(const std::wstring& filePath);

        static bool RemoveHideAttribute(const std::wstring& filePath);
        static bool GetDiskFreeSpaceValue(const wchar_t* path, unsigned long long* freeSpace);
    private:
        // 路径存在或者创建成功，返回true；否则返回false
        static bool CreateDirectoriesImpl(const std::wstring &path, bool showDebugWarning);
        static int RemoveAllImpl(const std::wstring &path, const std::function<bool(const DirectoryEntry &entry)> &filter);
        static void CopyImpl(const std::wstring& srcPath,const std::wstring& destPath, const std::function<bool(const DirectoryEntry &entry)> &filter);
        
    };
}

