// 简化版本的计算器图分析器，专门解决文件输出问题

#include <iostream>
#include <fstream>
#include <string>
#include <sstream>
#include <vector>
#include <windows.h>

// 简化的数据结构
struct SimpleCalculatorInfo
{
    std::wstring calculatorName;
    std::wstring elementId;
    std::wstring elementType;
    int outputDataId;
    std::vector<int> inputDataIds;
};

// 转换宽字符串为UTF-8字符串
std::string WStringToUTF8(const std::wstring& wstr)
{
    if (wstr.empty()) return std::string();
    
    int len = WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), -1, nullptr, 0, nullptr, nullptr);
    if (len <= 0) return std::string();
    
    std::string result(len - 1, 0);
    WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), -1, &result[0], len, nullptr, nullptr);
    return result;
}

// 生成Mermaid图
std::wstring GenerateSimpleMermaidGraph(const std::vector<SimpleCalculatorInfo>& calculatorInfos)
{
    std::wstringstream ss;
    ss << L"graph TD\n";

    // 创建节点
    for (size_t i = 0; i < calculatorInfos.size(); i++)
    {
        const SimpleCalculatorInfo& info = calculatorInfos[i];
        std::wstring nodeId = L"calc" + std::to_wstring(i);
        std::wstring nodeLabel = info.calculatorName + L"<br/>(" + info.elementType + L":" + info.elementId + L")";
        
        ss << L"    " << nodeId << L"[\"" << nodeLabel << L"\"]\n";
    }

    // 创建连接（简化版本）
    for (size_t i = 0; i < calculatorInfos.size(); i++)
    {
        const SimpleCalculatorInfo& info = calculatorInfos[i];
        std::wstring currentNodeId = L"calc" + std::to_wstring(i);

        // 简单的连接逻辑：如果有输入数据，连接到前一个节点
        if (!info.inputDataIds.empty() && i > 0)
        {
            std::wstring prevNodeId = L"calc" + std::to_wstring(i - 1);
            ss << L"    " << prevNodeId << L" --> " << currentNodeId << L"\n";
        }
    }

    return ss.str();
}

// 保存文件的函数
bool SaveMermaidGraphToFile(const std::wstring& filePath, const std::vector<SimpleCalculatorInfo>& calculatorInfos)
{
    // 生成Mermaid图
    std::wstring mermaidGraph = GenerateSimpleMermaidGraph(calculatorInfos);

    // 转换文件路径为多字节字符串
    std::string filePathMB = WStringToUTF8(filePath);
    if (filePathMB.empty())
    {
        return false;
    }

    // 打开文件（二进制模式以确保正确的UTF-8输出）
    std::ofstream outFile(filePathMB, std::ios::out | std::ios::binary);
    if (!outFile.is_open())
    {
        return false;
    }

    try
    {
        // 写入UTF-8 BOM（可选，但有助于某些编辑器识别编码）
        outFile.write("\xEF\xBB\xBF", 3);
        
        // 准备内容
        std::string header = "# 计算器关联更新图\n\n";
        std::string count = "分析了 " + std::to_string(calculatorInfos.size()) + " 个计算器\n\n";
        std::string mermaidStart = "```mermaid\n";
        std::string mermaidEnd = "\n```\n\n";
        std::string footer = "## 说明\n\n这是由CmdCalculatorGraphAnalyzer生成的计算器依赖关系图。\n";
        
        // 转换Mermaid图为UTF-8
        std::string mermaidGraphUtf8 = WStringToUTF8(mermaidGraph);
        
        // 写入内容
        outFile << header;
        outFile << count;
        outFile << mermaidStart;
        outFile << mermaidGraphUtf8;
        outFile << mermaidEnd;
        outFile << footer;
        
        outFile.close();
        return true;
    }
    catch (...)
    {
        outFile.close();
        return false;
    }
}

// 测试函数
void TestMermaidGraphGeneration()
{
    // 创建测试数据
    std::vector<SimpleCalculatorInfo> testData;
    
    SimpleCalculatorInfo calc1;
    calc1.calculatorName = L"墙参数计算器";
    calc1.elementId = L"12345";
    calc1.elementType = L"Wall";
    calc1.outputDataId = 1001;
    calc1.inputDataIds = {};
    testData.push_back(calc1);
    
    SimpleCalculatorInfo calc2;
    calc2.calculatorName = L"墙图形计算器";
    calc2.elementId = L"12345";
    calc2.elementType = L"Wall";
    calc2.outputDataId = 1002;
    calc2.inputDataIds = {1001};
    testData.push_back(calc2);
    
    SimpleCalculatorInfo calc3;
    calc3.calculatorName = L"楼板图形计算器";
    calc3.elementId = L"67890";
    calc3.elementType = L"Floor";
    calc3.outputDataId = 2001;
    calc3.inputDataIds = {1002};
    testData.push_back(calc3);

    // 保存到文件
    std::wstring filePath = L"test_calculator_graph.md";
    bool success = SaveMermaidGraphToFile(filePath, testData);
    
    if (success)
    {
        std::wcout << L"文件保存成功: " << filePath << std::endl;
    }
    else
    {
        std::wcout << L"文件保存失败!" << std::endl;
    }
}

// 主函数（用于测试）
int main()
{
    // 设置控制台输出为UTF-8
    SetConsoleOutputCP(CP_UTF8);
    
    std::wcout << L"开始测试Mermaid图生成..." << std::endl;
    TestMermaidGraphGeneration();
    std::wcout << L"测试完成!" << std::endl;
    
    return 0;
}
