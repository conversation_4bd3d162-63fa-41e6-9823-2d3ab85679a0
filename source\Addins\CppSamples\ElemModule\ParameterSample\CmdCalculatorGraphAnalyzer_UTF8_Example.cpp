// CmdCalculatorGraphAnalyzer 使用UTF-8方案的示例代码
// 这个文件展示了如何在ExecuteCommand中正确使用UTF-8文件保存

#include "CmdCalculatorGraphAnalyzer.h"
#include "UTF8FileHelper.h"
// ... 其他必要的头文件

// 在ExecuteCommand函数中的关键部分
gcmp::OwnerPtr<gcmp::IAction> CmdCalculatorGraphAnalyzer::ExecuteCommand(const gcmp::CommandParameters& cmdParams)
{
    IDocument* pDoc = UiDocumentViewUtils::GetCurrentDocument();
    if (!pDoc)
    {
        // 没有打开的文档
        return nullptr;
    }

    OwnerPtr<IUserTransaction> opUserTrans = IUserTransaction::Create(pDoc, L"分析计算器关联图");

    // 1. 分析计算器
    std::vector<CalculatorInfo> calculatorInfos;
    AnalyzeElementCalculators(pDoc, calculatorInfos);

    if (calculatorInfos.empty())
    {
        // 没有找到计算器
        opUserTrans->Commit();
        return nullptr;
    }

    // 2. 生成Mermaid图
    std::wstring mermaidGraph = GenerateMermaidGraph(calculatorInfos);

    // 3. 获取保存路径
    std::wstring filePathName = gcmp::UiCommonDialog::ShowSaveFileDialog(
        GBMP_TR(L"请选择导出mermaid文件"),
        L"", 
        GBMP_TR(L"markdown文件(*.md)"));

    // 4. 使用UTF-8方案保存文件
    if (!filePathName.empty())
    {
        bool success = UTF8FileHelper::SaveMermaidGraphFile(
            filePathName, 
            mermaidGraph, 
            calculatorInfos.size());

        if (success)
        {
            // 可以添加成功提示
            // 例如：显示消息框或状态栏提示
            std::wstring message = L"计算器关联图已成功保存到: " + filePathName;
            // ShowInfoMessage(message);
        }
        else
        {
            // 可以添加失败提示
            std::wstring message = L"文件保存失败，请检查路径和权限";
            // ShowErrorMessage(message);
        }
    }
    else
    {
        // 用户取消了文件保存
        // 这是正常情况，不需要错误提示
    }

    opUserTrans->Commit();
    return nullptr;
}

// 生成Mermaid图的示例实现
std::wstring CmdCalculatorGraphAnalyzer::GenerateMermaidGraph(const std::vector<CalculatorInfo>& calculatorInfos)
{
    std::wstringstream ss;
    ss << L"graph TD\n";

    // 创建节点映射
    std::map<RegenDataId, int> outputToCalculatorMap;
    for (size_t i = 0; i < calculatorInfos.size(); i++)
    {
        outputToCalculatorMap[calculatorInfos[i].outputDataId] = static_cast<int>(i);
    }

    // 生成节点
    for (size_t i = 0; i < calculatorInfos.size(); i++)
    {
        const CalculatorInfo& info = calculatorInfos[i];
        std::wstring nodeId = L"calc" + std::to_wstring(i);
        
        // 创建节点标签，包含中文
        std::wstring nodeLabel = info.calculatorName + L"<br/>(" + 
                                info.elementType + L":" + info.elementId + L")";
        
        ss << L"    " << nodeId << L"[\"" << nodeLabel << L"\"]\n";
    }

    ss << L"\n";

    // 生成连接
    std::set<std::pair<int, int>> connections;
    for (size_t i = 0; i < calculatorInfos.size(); i++)
    {
        const CalculatorInfo& info = calculatorInfos[i];
        
        for (const RegenDataId& inputDataId : info.inputDataIds)
        {
            auto it = outputToCalculatorMap.find(inputDataId);
            if (it != outputToCalculatorMap.end())
            {
                int sourceIndex = it->second;
                int targetIndex = static_cast<int>(i);
                
                // 避免重复连接
                if (connections.find({sourceIndex, targetIndex}) == connections.end())
                {
                    std::wstring sourceNodeId = L"calc" + std::to_wstring(sourceIndex);
                    std::wstring targetNodeId = L"calc" + std::to_wstring(targetIndex);
                    
                    ss << L"    " << sourceNodeId << L" --> " << targetNodeId << L"\n";
                    connections.insert({sourceIndex, targetIndex});
                }
            }
        }
    }

    // 添加样式
    ss << L"\n";
    for (size_t i = 0; i < calculatorInfos.size(); i++)
    {
        const CalculatorInfo& info = calculatorInfos[i];
        std::wstring nodeId = L"calc" + std::to_wstring(i);
        std::wstring color;
        
        // 根据图元类型设置颜色
        if (info.elementType.find(L"Wall") != std::wstring::npos)
        {
            color = L"#e1f5fe";  // 蓝色 - 墙
        }
        else if (info.elementType.find(L"Floor") != std::wstring::npos)
        {
            color = L"#f3e5f5";  // 紫色 - 楼板
        }
        else if (info.elementType.find(L"Beam") != std::wstring::npos)
        {
            color = L"#e8f5e8";  // 绿色 - 梁
        }
        else
        {
            color = L"#f5f5f5";  // 灰色 - 其他
        }
        
        ss << L"    style " << nodeId << L" fill:" << color << L"\n";
    }

    return ss.str();
}

// 使用UTF-8方案的优势示例
void DemonstrateUTF8Advantages()
{
    // 1. 支持完整的中文字符
    std::wstring chineseText = L"墙参数计算器、楼板图形计算器、梁截面计算器";
    std::string utf8Text = UTF8FileHelper::WStringToUTF8(chineseText);
    
    // 2. 自动处理文件编码
    UTF8FileHelper::SaveUTF8File(L"test.txt", utf8Text);
    
    // 3. 生成完整的Markdown文档
    std::wstring mermaidGraph = L"graph TD\n    A[\"开始\"] --> B[\"结束\"]\n";
    UTF8FileHelper::SaveMermaidGraphFile(L"demo.md", mermaidGraph, 2);
    
    // 4. 错误处理
    bool success = UTF8FileHelper::SaveMermaidGraphFile(L"", mermaidGraph, 0);
    if (!success)
    {
        // 处理保存失败的情况
    }
}

// 测试不同编码方案的对比
void CompareEncodingMethods()
{
    std::wstring testText = L"测试中文：墙、楼板、梁";
    
    // 方法1：UTF-8方案（推荐）
    bool utf8Success = UTF8FileHelper::SaveUTF8File(L"utf8_test.txt", 
        UTF8FileHelper::WStringToUTF8(testText));
    
    // 方法2：原始wofstream方案（有问题）
    std::wofstream wfile(L"wofstream_test.txt");
    wfile << testText;
    wfile.close();
    
    // 方法3：ASCII方案（兼容但丢失中文）
    std::ofstream afile("ascii_test.txt");
    afile << "Test Chinese: Wall, Floor, Beam";  // 英文替代
    afile.close();
    
    // 结果：只有UTF-8方案能正确保存和显示中文
}
