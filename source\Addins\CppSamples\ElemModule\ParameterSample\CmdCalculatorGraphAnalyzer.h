﻿#pragma once

#include "CommandBase.h"
#include "RegenDataId.h"
#include <vector>
#include <string>

namespace gcmp
{
    class IDocument;
    class IElement;
}

using namespace gcmp;

namespace Sample
{
    class CmdCalculatorGraphAnalyzer : public CommandBase
    {
    public:
        CmdCalculatorGraphAnalyzer();

    public:
        virtual OwnerPtr<IAction> ExecuteCommand(const gcmp::CommandParameters& cmdParams) override;
        virtual bool IsEnabled() const override { return true; }
        virtual bool IsVisible() const override { return true; }
        virtual bool ShouldClearSelectionBeforeExecution() const override { return true; }

    private:
        struct CalculatorInfo
        {
            std::wstring calculatorName;
            std::wstring elementId;
            std::wstring elementType;
            RegenDataId outputDataId;
            std::vector<RegenDataId> inputDataIds;
        };

        void AnalyzeElementCalculators(IDocument* pDoc, std::vector<CalculatorInfo>& calculatorInfos);
        std::wstring GenerateMermaidGraph(const std::vector<CalculatorInfo>& calculatorInfos);
        std::wstring RegenDataIdToString(const RegenDataId& dataId);
        std::wstring GetElementTypeName(IElement* pElement);
    };
}
